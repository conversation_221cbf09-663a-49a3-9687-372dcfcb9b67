# 图片标注系统

这是一个基于Flask的Web图片标注系统，用于对缩略图进行分类标注。

## 功能特性

- 🖼️ Web界面展示缩略图进行标注
- 🏷️ 支持四个分类：小白、小黑、小花、无
- 👥 支持多人同时标注，无冲突
- 📊 实时显示标注统计信息
- 💾 自动保存标注结果到JSON文件
- ⌨️ 支持键盘快捷键操作
- 📱 响应式设计，支持移动设备

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务

```bash
python app.py
```

### 3. 访问系统

在浏览器中访问：`http://localhost:5000`

如需在局域网中访问，请使用服务器的IP地址：`http://[服务器IP]:5000`

## 使用说明

### 基本操作

1. **输入用户名**：首次使用时输入您的用户名
2. **开始标注**：点击"开始标注"按钮
3. **选择分类**：点击对应的分类按钮进行标注
   - 🐱 小白（快捷键：1）
   - 🐱 小黑（快捷键：2）
   - 🐱 小花（快捷键：3）
   - ❌ 无（快捷键：4）
4. **跳过图片**：点击"跳过此图片"或按空格键

### 键盘快捷键

- `1` - 标注为"小白"
- `2` - 标注为"小黑" 
- `3` - 标注为"小花"
- `4` - 标注为"无"
- `空格` - 跳过当前图片

### 多人协作

- 每个用户使用不同的用户名
- 系统自动分配不同的图片给不同用户
- 使用文件锁机制防止标注冲突
- 实时更新标注统计信息

## 数据文件

### annotations.json
包含所有标注结果的JSON文件，格式如下：

```json
{
  "2025-01-23_20-46-05_hls.jpg": {
    "category": "小白",
    "user_id": "用户名",
    "timestamp": "2024-01-01T12:00:00.000000"
  }
}
```

### 导出功能
点击"导出标注结果"按钮可以查看完整的标注数据。

## 配置说明

在 `app.py` 中可以修改以下配置：

- `THUMBNAIL_DIR`：缩略图目录路径
- `ANNOTATIONS_FILE`：标注结果文件名
- `CATEGORIES`：标注分类列表
- 服务端口：默认5000

## 技术特性

- **并发安全**：使用文件锁确保多用户同时标注时数据一致性
- **负载均衡**：根据用户ID分配不同图片，减少冲突
- **实时统计**：每30秒自动更新标注进度
- **错误处理**：完整的错误处理和用户提示
- **响应式设计**：适配桌面和移动设备

## 注意事项

1. 确保缩略图目录路径正确
2. 系统会创建 `annotations.json` 和 `annotations.lock` 文件
3. 建议定期备份标注结果文件
4. 多人使用时请使用不同的用户名 