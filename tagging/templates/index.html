<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片标注系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .user-input {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .user-input label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .user-input input {
            width: 200px;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .user-input button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .user-input button:hover {
            background: #5a6fd8;
        }
        
        .stats {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .annotation-area {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
        
        .image-container {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .current-image {
            max-width: 100%;
            max-height: 500px;
            border: 3px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .image-name {
            font-size: 1.2em;
            color: #666;
            margin-top: 10px;
        }
        
        .category-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .category-btn {
            padding: 15px;
            font-size: 18px;
            font-weight: bold;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .category-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
        
        .category-btn.xiaobai {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .category-btn.xiaohei {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .category-btn.xiaohua {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .category-btn.wu {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 24px;
            font-size: 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .category-buttons {
                grid-template-columns: 1fr 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏷️ 图片标注系统</h1>
            <p>分类标注：小白 | 小黑 | 小花 | 无</p>
        </div>
        
        <div class="user-input">
            <label for="userId">请输入您的用户名：</label>
            <input type="text" id="userId" placeholder="输入用户名" />
            <button onclick="startAnnotation()">开始标注</button>
        </div>
        
        <div class="stats">
            <h3>📊 标注统计</h3>
            <div class="stats-grid" id="statsGrid">
                <!-- 统计信息将通过JavaScript动态加载 -->
            </div>
        </div>
        
        <div class="annotation-area" id="annotationArea">
            <div class="image-container">
                <img id="currentImage" class="current-image" alt="当前图片" />
                <div id="imageName" class="image-name"></div>
            </div>
            
            <div class="category-buttons">
                <button class="category-btn xiaobai" onclick="selectCategory('小白')">🐱 小白</button>
                <button class="category-btn xiaohei" onclick="selectCategory('小黑')">🐱 小黑</button>
                <button class="category-btn xiaohua" onclick="selectCategory('小花')">🐱 小花</button>
                <button class="category-btn wu" onclick="selectCategory('无')">❌ 无</button>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="loadNextImage()">跳过此图片</button>
                <button class="btn btn-secondary" onclick="exportAnnotations()">导出标注结果</button>
            </div>
        </div>
        
        <div id="messageArea"></div>
    </div>

    <script>
        let currentUserId = '';
        let currentImageName = '';

        function showMessage(text, type = 'info') {
            const messageArea = document.getElementById('messageArea');
            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.textContent = text;
            messageArea.appendChild(message);
            
            setTimeout(() => {
                message.remove();
            }, 5000);
        }

        function updateStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    const statsGrid = document.getElementById('statsGrid');
                    statsGrid.innerHTML = `
                        <div class="stat-item">
                            <div class="stat-number">${data.total}</div>
                            <div class="stat-label">总图片数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.annotated}</div>
                            <div class="stat-label">已标注</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.remaining}</div>
                            <div class="stat-label">待标注</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.categories['小白']}</div>
                            <div class="stat-label">小白</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.categories['小黑']}</div>
                            <div class="stat-label">小黑</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.categories['小花']}</div>
                            <div class="stat-label">小花</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.categories['无']}</div>
                            <div class="stat-label">无</div>
                        </div>
                    `;
                })
                .catch(error => {
                    console.error('获取统计信息失败:', error);
                });
        }

        function startAnnotation() {
            const userId = document.getElementById('userId').value.trim();
            if (!userId) {
                showMessage('请输入用户名', 'error');
                return;
            }
            
            currentUserId = userId;
            document.getElementById('annotationArea').style.display = 'block';
            loadNextImage();
            showMessage(`欢迎 ${userId}！开始标注`, 'success');
        }

        function loadNextImage() {
            if (!currentUserId) {
                showMessage('请先输入用户名', 'error');
                return;
            }

            // 显示加载状态
            const imageContainer = document.querySelector('.image-container');
            imageContainer.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在加载下一张图片...</p>
                </div>
            `;

            fetch(`/api/next_image?user_id=${encodeURIComponent(currentUserId)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentImageName = data.image_name;
                        imageContainer.innerHTML = `
                            <img id="currentImage" class="current-image" src="${data.image_url}" alt="当前图片" />
                            <div id="imageName" class="image-name">${data.image_name}</div>
                        `;
                        updateStats();
                    } else {
                        imageContainer.innerHTML = `
                            <div class="message success">
                                <h3>🎉 恭喜！</h3>
                                <p>${data.message}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载图片失败:', error);
                    showMessage('加载图片失败，请重试', 'error');
                    imageContainer.innerHTML = `
                        <div class="message error">
                            <p>加载失败，请重试</p>
                        </div>
                    `;
                });
        }

        function selectCategory(category) {
            if (!currentImageName) {
                showMessage('没有可标注的图片', 'error');
                return;
            }

            const data = {
                image_name: currentImageName,
                category: category,
                user_id: currentUserId
            };

            fetch('/api/annotate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`标注为"${category}"成功！`, 'success');
                    loadNextImage();
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('保存标注失败:', error);
                showMessage('保存标注失败，请重试', 'error');
            });
        }

        function exportAnnotations() {
            window.open('/export', '_blank');
        }

        // 页面加载时更新统计信息
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            // 每30秒更新一次统计信息
            setInterval(updateStats, 30000);
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(event) {
            if (!currentImageName) return;
            
            switch(event.key) {
                case '1':
                    selectCategory('小白');
                    break;
                case '2':
                    selectCategory('小黑');
                    break;
                case '3':
                    selectCategory('小花');
                    break;
                case '4':
                    selectCategory('无');
                    break;
                case ' ':
                    event.preventDefault();
                    loadNextImage();
                    break;
            }
        });
    </script>
</body>
</html> 