#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import glob
import random
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_from_directory
import fcntl
import time

app = Flask(__name__)

# 配置
THUMBNAIL_DIR = "./renamed_thumbnails"
ANNOTATIONS_FILE = "annotations.json"
LOCK_FILE = "annotations.lock"

# 标注类别
CATEGORIES = ["小白", "小黑", "小花", "无"]

def load_annotations():
    """加载已有的标注数据"""
    if os.path.exists(ANNOTATIONS_FILE):
        try:
            with open(ANNOTATIONS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            return {}
    return {}

def save_annotations(annotations):
    """安全保存标注数据（使用文件锁防止并发冲突）"""
    max_retries = 10
    retry_delay = 0.1
    
    for attempt in range(max_retries):
        try:
            with open(LOCK_FILE, 'w') as lock_file:
                fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                
                # 重新读取最新数据以防其他进程已更新
                current_annotations = load_annotations()
                current_annotations.update(annotations)
                
                with open(ANNOTATIONS_FILE, 'w', encoding='utf-8') as f:
                    json.dump(current_annotations, f, ensure_ascii=False, indent=2)
                
                fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
                return True
                
        except (IOError, OSError):
            time.sleep(retry_delay)
            retry_delay *= 2  # 指数退避
            
    return False

def get_image_list():
    """获取所有图片文件列表"""
    pattern = os.path.join(THUMBNAIL_DIR, "*.jpg")
    image_files = glob.glob(pattern)
    return [os.path.basename(f) for f in sorted(image_files)]

def get_next_untagged_image(user_id):
    """为指定用户获取下一张未标注的图片"""
    annotations = load_annotations()
    image_list = get_image_list()
    
    # 找到未标注的图片
    untagged_images = [img for img in image_list if img not in annotations]
    
    if not untagged_images:
        return None
    
    # 为了减少冲突，根据用户ID和时间戳生成伪随机种子
    seed = hash(user_id) + int(time.time()) % 1000
    random.seed(seed)
    
    # 随机选择一张未标注的图片
    return random.choice(untagged_images)

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html', categories=CATEGORIES)

@app.route('/api/next_image')
def next_image():
    """获取下一张需要标注的图片"""
    user_id = request.args.get('user_id', 'anonymous')
    image_name = get_next_untagged_image(user_id)
    
    if image_name:
        return jsonify({
            'success': True,
            'image_name': image_name,
            'image_url': f'/images/{image_name}'
        })
    else:
        return jsonify({
            'success': False,
            'message': '所有图片都已标注完成！'
        })

@app.route('/api/annotate', methods=['POST'])
def annotate():
    """保存标注结果"""
    data = request.json
    image_name = data.get('image_name')
    category = data.get('category')
    user_id = data.get('user_id', 'anonymous')
    
    if not image_name or category not in CATEGORIES:
        return jsonify({'success': False, 'message': '参数错误'})
    
    # 创建标注记录
    annotation = {
        image_name: {
            'category': category,
            'user_id': user_id,
            'timestamp': datetime.now().isoformat()
        }
    }
    
    # 保存标注
    if save_annotations(annotation):
        return jsonify({'success': True, 'message': '标注保存成功'})
    else:
        return jsonify({'success': False, 'message': '保存失败，请重试'})

@app.route('/api/stats')
def stats():
    """获取标注统计信息"""
    annotations = load_annotations()
    total_images = len(get_image_list())
    annotated_count = len(annotations)
    remaining_count = total_images - annotated_count
    
    # 统计各类别数量
    category_stats = {cat: 0 for cat in CATEGORIES}
    for annotation in annotations.values():
        category = annotation.get('category')
        if category in category_stats:
            category_stats[category] += 1
    
    return jsonify({
        'total': total_images,
        'annotated': annotated_count,
        'remaining': remaining_count,
        'categories': category_stats
    })

@app.route('/images/<filename>')
def serve_image(filename):
    """提供图片文件"""
    return send_from_directory(THUMBNAIL_DIR, filename)

@app.route('/export')
def export_annotations():
    """导出标注结果"""
    annotations = load_annotations()
    return jsonify(annotations)

if __name__ == '__main__':
    # 确保必要的目录存在
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    # 启动应用
    app.run(host='0.0.0.0', port=5000, debug=True) 
