# 影子模式部署架构设计

## 概述

本文档描述了将红外猫识别模型集成到caby_vision服务作为影子模式部署的完整架构设计。

## 架构组件

### 1. caby_vision 扩展

#### 1.1 新增特征模型集成
- **模型名称**: `featured_cat_recognition` (替换原有的"infrared"命名)
- **模型类型**: ONNX量化模型 (`infrared_cat_model_quantized.onnx`)
- **集成方式**: 在现有triton server结构中添加新的模型目录
- **支持模式**: CPU和GPU执行

#### 1.2 新增API端点
```
POST /featured/extract
```
**功能**: 提取猫咪特征向量（影子模式专用）
**输入**:
```json
{
  "image": "base64_encoded_image",
  "user_id": "user_identifier",
  "task": "extract_features"
}
```
**输出**:
```json
{
  "success": true,
  "features": [0.123, 0.456, ...],  // 特征向量
  "feature_dim": 768,
  "model_version": "featured_v1.0",
  "timestamp": "2025-01-14T10:00:00Z"
}
```

### 2. Qdrant向量数据库设计

#### 2.1 集合结构
```
Collection: cat_features_{user_id}
```

#### 2.2 向量存储格式
```json
{
  "id": "cat_id",
  "vector": [0.123, 0.456, ...],
  "payload": {
    "cat_id": "f3ce1b02b2c1d755421000",
    "cat_name": "小白",
    "user_id": "user_001",
    "created_at": "2025-01-14T10:00:00Z",
    "updated_at": "2025-01-14T10:00:00Z",
    "feature_version": "featured_v1.0"
  }
}
```

#### 2.3 用户权限控制
- 每个用户拥有独立的collection: `cat_features_{user_id}`
- 查询时只能访问自己的collection
- 支持批量操作和增量更新

### 3. caby_ai 服务扩展

#### 3.1 双API调用流程
```
1. 接收识别请求
2. 并行调用:
   - 现有API: /predict (获取分类结果)
   - 影子API: /featured/extract (获取特征向量)
3. 特征向量发送到Qdrant进行相似度比较
4. 根据相似度阈值决定分类结果
5. 存储两种结果到数据库
```

#### 3.2 相似度比较逻辑
```go
type SimilarityConfig struct {
    Threshold       float64 `json:"threshold"`        // 0.85
    NewCatThreshold float64 `json:"new_cat_threshold"` // 0.70
    TopK           int     `json:"top_k"`            // 5
}

type ShadowModeResult struct {
    OriginalResult   string  `json:"original_result"`
    ShadowResult     string  `json:"shadow_result"`
    Similarity       float64 `json:"similarity"`
    IsNewCat        bool    `json:"is_new_cat"`
    MatchedCatID    string  `json:"matched_cat_id"`
    Confidence      float64 `json:"confidence"`
}
```

### 4. 数据库扩展

#### 4.1 recordshit表扩展
```sql
ALTER TABLE record_analysis ADD COLUMN shadow_mode_result TEXT;
ALTER TABLE record_analysis ADD COLUMN shadow_similarity DECIMAL(5,4);
ALTER TABLE record_analysis ADD COLUMN shadow_matched_cat_id VARCHAR(32);
ALTER TABLE record_analysis ADD COLUMN shadow_is_new_cat BOOLEAN DEFAULT FALSE;
ALTER TABLE record_analysis ADD COLUMN shadow_confidence DECIMAL(5,4);
```

#### 4.2 新增影子模式配置表
```sql
CREATE TABLE shadow_mode_config (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(20) NOT NULL,
  similarity_threshold DECIMAL(5,4) DEFAULT 0.8500,
  new_cat_threshold DECIMAL(5,4) DEFAULT 0.7000,
  enabled BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

### 5. Cat ID管理系统

#### 5.1 统一ID生成
```go
func GenerateNewCatID() string {
    timestamp := time.Now().Format("20060102150405")
    return fmt.Sprintf("NewCat%s", timestamp)
}
```

#### 5.2 跨服务同步机制
```
1. caby_ai检测到新猫 -> 生成临时ID
2. 调用backend_server API创建猫咪记录
3. backend_server返回正式cat_id
4. caby_ai更新Qdrant中的记录
5. 同步完成
```

### 6. 用户通知系统

#### 6.1 消息格式
```
原始结果: "小白"
影子模式结果: "小花"
最终消息: "小白（小花）"
```

#### 6.2 通知触发条件
- 影子模式结果与原始结果不一致
- 检测到新猫咪
- 相似度低于阈值

## 部署配置

### 1. 环境变量配置
```bash
# caby_vision
FEATURED_MODEL_PATH=/app/models/featured/infrared_cat_model_quantized.onnx
FEATURED_REFERENCE_PATH=/app/models/featured/reference_features.json
SHADOW_MODE_ENABLED=true

# caby_ai
QDRANT_HOST=qdrant
QDRANT_PORT=6333
SHADOW_SIMILARITY_THRESHOLD=0.85
SHADOW_NEW_CAT_THRESHOLD=0.70

# backend_server
SHADOW_MODE_NOTIFICATIONS=true
```

### 2. Docker Compose扩展
```yaml
services:
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333

volumes:
  qdrant_data:
```

## ✅ 实施完成状态

所有阶段已完成部署：

1. ✅ **caby_vision模型集成和API扩展** - 特征提取API已集成
2. ✅ **Qdrant数据库设置** - 运行在caby_ai中，用户权限隔离
3. ✅ **caby_ai双API调用和相似度比较** - 完整的影子模式逻辑
4. ✅ **数据库扩展和存储逻辑** - 影子模式结果存储
5. ✅ **Cat ID管理和跨服务同步** - NewCat<DateTime>命名
6. ✅ **用户通知系统** - "小白（小花）"格式通知
7. ✅ **配置和部署优化** - 简化架构，只有caby_ai访问Qdrant
8. ✅ **测试和验证** - 完整的测试套件

## 🎯 最终架构优化

**核心改进**:
- **简化设计**: 只有caby_ai访问Qdrant，backend_server只接收结果
- **职责清晰**: caby_ai负责所有向量分析，backend_server负责数据存储和通知
- **部署简单**: 统一的部署脚本和文档

## 技术考虑

### 1. 性能优化
- 使用量化ONNX模型减少内存占用
- 异步处理影子模式请求
- Qdrant向量索引优化

### 2. 可维护性
- 模型版本管理
- 配置热更新
- 监控和日志

### 3. 扩展性
- 支持多模型并行
- 用户自定义阈值
- 渐进式模型替换
