#!/usr/bin/env python3
"""
红外猫咪识别推理包装器
使用ONNX模型进行快速推理
支持CPU和GPU运行模式
"""

import os
import json
import time
import base64
import io
from typing import Dict, Tuple, List, Optional, Union
import numpy as np
from PIL import Image
import onnxruntime as ort
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler
import torch
import logging

logger = logging.getLogger(__name__)

class InfraredCatRecognizer:
    """红外猫咪识别器"""
    
    def __init__(self,
                 onnx_model_path: str,
                 reference_features_path: str = None,
                 device: str = "auto"):
        """
        初始化红外猫咪识别器

        Args:
            onnx_model_path: ONNX模型路径
            reference_features_path: 参考特征数据库路径
            device: 运行设备 ("auto", "cpu", "cuda")
        """
        self.onnx_model_path = onnx_model_path
        self.reference_features_path = reference_features_path
        self.device = device

        # 类别映射
        self.cat_to_id = {"小白": 0, "小花": 1, "小黑": 2}
        self.id_to_cat = {"0": "小白", "1": "小花", "2": "小黑"}

        # 设置ONNX运行时提供者
        providers = self._get_providers()

        # 加载ONNX模型
        self.session = ort.InferenceSession(onnx_model_path, providers=providers)
        self.input_name = self.session.get_inputs()[0].name

        # 特征标准化器
        self.scaler = StandardScaler()
        self.reference_features = None
        self.reference_labels = None

        # 加载参考特征（如果提供）
        if reference_features_path and os.path.exists(reference_features_path):
            self.load_reference_features(reference_features_path)

        logger.info(f"InfraredCatRecognizer initialized with device: {device}, providers: {providers}")

    def _get_providers(self) -> List[str]:
        """获取ONNX运行时提供者"""
        if self.device == "cpu":
            return ["CPUExecutionProvider"]
        elif self.device == "cuda":
            if "CUDAExecutionProvider" in ort.get_available_providers():
                return ["CUDAExecutionProvider", "CPUExecutionProvider"]
            else:
                logger.warning("CUDA not available, falling back to CPU")
                return ["CPUExecutionProvider"]
        else:  # auto
            available_providers = ort.get_available_providers()
            if "CUDAExecutionProvider" in available_providers:
                return ["CUDAExecutionProvider", "CPUExecutionProvider"]
            else:
                return ["CPUExecutionProvider"]
    
    def preprocess_image(self, image_input: Union[str, np.ndarray, Image.Image]) -> np.ndarray:
        """
        预处理图像

        Args:
            image_input: 图像输入，可以是文件路径、numpy数组或PIL图像

        Returns:
            预处理后的图像数组
        """
        try:
            # 处理不同类型的输入
            if isinstance(image_input, str):
                # 文件路径
                image = Image.open(image_input).convert('RGB')
            elif isinstance(image_input, np.ndarray):
                # numpy数组
                if image_input.shape[-1] == 3:  # RGB
                    image = Image.fromarray(image_input.astype(np.uint8))
                else:  # BGR
                    image_rgb = image_input[:, :, ::-1]  # BGR to RGB
                    image = Image.fromarray(image_rgb.astype(np.uint8))
            elif isinstance(image_input, Image.Image):
                # PIL图像
                image = image_input.convert('RGB')
            else:
                raise ValueError(f"不支持的图像输入类型: {type(image_input)}")

            # 调整大小
            image = image.resize((224, 224))

            # 转换为numpy数组并归一化
            image_array = np.array(image, dtype=np.float32) / 255.0

            # 标准化
            mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
            std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
            image_array = (image_array - mean) / std

            # 调整维度 (H, W, C) -> (1, C, H, W)
            image_array = np.transpose(image_array, (2, 0, 1))
            image_array = np.expand_dims(image_array, axis=0)

            return image_array

        except Exception as e:
            raise ValueError(f"图像预处理失败: {e}")
    
    def preprocess_image_from_base64(self, image_base64: str) -> np.ndarray:
        """
        从base64字符串预处理图像

        Args:
            image_base64: base64编码的图像字符串

        Returns:
            预处理后的图像数组
        """
        try:
            # 解码base64
            image_bytes = base64.b64decode(image_base64)
            image = Image.open(io.BytesIO(image_bytes))

            # 使用通用预处理方法
            return self.preprocess_image(image)

        except Exception as e:
            raise ValueError(f"Base64图像预处理失败: {e}")

    def extract_features(self, image_input: Union[str, np.ndarray, Image.Image]) -> np.ndarray:
        """
        提取图像特征

        Args:
            image_input: 图像输入，可以是文件路径、numpy数组或PIL图像

        Returns:
            特征向量
        """
        # 预处理图像
        input_data = self.preprocess_image(image_input)

        # ONNX推理
        outputs = self.session.run(None, {self.input_name: input_data})

        # 返回特征向量
        features = outputs[1]  # features是第二个输出
        return features.flatten()

    def extract_features_from_base64(self, image_base64: str) -> np.ndarray:
        """
        从base64字符串提取图像特征

        Args:
            image_base64: base64编码的图像字符串

        Returns:
            特征向量
        """
        # 预处理图像
        input_data = self.preprocess_image_from_base64(image_base64)

        # ONNX推理
        outputs = self.session.run(None, {self.input_name: input_data})

        # 返回特征向量
        features = outputs[1]  # features是第二个输出
        return features.flatten()
    
    def load_reference_features(self, features_path: str):
        """加载参考特征数据库"""
        with open(features_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.reference_features = np.array(data['features'])
        self.reference_labels = np.array(data['labels'])
        
        # 标准化特征
        self.reference_features = self.scaler.fit_transform(self.reference_features)
    
    def predict(self, image_input: Union[str, np.ndarray, Image.Image], k: int = 7) -> Tuple[str, float]:
        """
        预测图像类别

        Args:
            image_input: 图像输入，可以是文件路径、numpy数组或PIL图像
            k: KNN的k值

        Returns:
            (预测的猫咪名称, 置信度)
        """
        if self.reference_features is None:
            raise ValueError("未加载参考特征数据库")

        # 提取特征
        features = self.extract_features(image_input)
        features_scaled = self.scaler.transform(features.reshape(1, -1))

        # KNN分类
        knn = KNeighborsClassifier(n_neighbors=min(k, len(self.reference_features)), metric='cosine')
        knn.fit(self.reference_features, self.reference_labels)

        # 预测
        pred_label = knn.predict(features_scaled)[0]
        pred_proba = knn.predict_proba(features_scaled)[0]
        confidence = max(pred_proba)

        predicted_cat = self.id_to_cat[str(pred_label)]

        return predicted_cat, float(confidence)

    def predict_from_base64(self, image_base64: str, k: int = 7) -> Tuple[str, float]:
        """
        从base64字符串预测图像类别

        Args:
            image_base64: base64编码的图像字符串
            k: KNN的k值

        Returns:
            (预测的猫咪名称, 置信度)
        """
        if self.reference_features is None:
            raise ValueError("未加载参考特征数据库")

        # 提取特征
        features = self.extract_features_from_base64(image_base64)
        features_scaled = self.scaler.transform(features.reshape(1, -1))

        # KNN分类
        knn = KNeighborsClassifier(n_neighbors=min(k, len(self.reference_features)), metric='cosine')
        knn.fit(self.reference_features, self.reference_labels)

        # 预测
        pred_label = knn.predict(features_scaled)[0]
        pred_proba = knn.predict_proba(features_scaled)[0]
        confidence = max(pred_proba)

        predicted_cat = self.id_to_cat[str(pred_label)]

        return predicted_cat, float(confidence)
    
    def batch_predict(self, image_paths: List[str], k: int = 7) -> List[Tuple[str, float]]:
        """批量预测"""
        results = []
        for image_path in image_paths:
            try:
                result = self.predict(image_path, k)
                results.append(result)
            except Exception as e:
                results.append((None, 0.0))
        return results
    
    def benchmark(self, image_paths: List[str], num_runs: int = 10) -> Dict:
        """性能基准测试"""
        if not image_paths:
            raise ValueError("需要提供测试图像路径")
        
        # 预热
        for _ in range(3):
            self.extract_features(image_paths[0])
        
        # 测试特征提取速度
        start_time = time.time()
        for _ in range(num_runs):
            for image_path in image_paths:
                self.extract_features(image_path)
        end_time = time.time()
        
        total_images = len(image_paths) * num_runs
        total_time = end_time - start_time
        throughput = total_images / total_time
        
        return {
            'total_images': total_images,
            'total_time': total_time,
            'throughput': throughput,
            'avg_time_per_image': total_time / total_images
        }

# 使用示例
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="红外猫咪识别推理")
    parser.add_argument("--model", type=str, required=True, help="ONNX模型路径")
    parser.add_argument("--reference", type=str, help="参考特征数据库路径")
    parser.add_argument("--image", type=str, help="单张图像路径")
    parser.add_argument("--images", type=str, nargs='+', help="多张图像路径")
    parser.add_argument("--benchmark", action='store_true', help="运行性能基准测试")
    
    args = parser.parse_args()
    
    # 创建识别器
    recognizer = InfraredCatRecognizer(args.model, args.reference)
    
    if args.image:
        # 单张图像预测
        cat, confidence = recognizer.predict(args.image)
        print(f"预测结果: {cat} (置信度: {confidence:.4f})")
    
    elif args.images:
        # 多张图像预测
        results = recognizer.batch_predict(args.images)
        for i, (cat, confidence) in enumerate(results):
            print(f"图像 {i+1}: {cat} (置信度: {confidence:.4f})")
    
    if args.benchmark and (args.image or args.images):
        # 性能基准测试
        test_images = [args.image] if args.image else args.images
        benchmark_results = recognizer.benchmark(test_images)
        print(f"\n性能基准测试结果:")
        print(f"  总图像数: {benchmark_results['total_images']}")
        print(f"  总耗时: {benchmark_results['total_time']:.2f} 秒")
        print(f"  吞吐量: {benchmark_results['throughput']:.2f} 图像/秒")
        print(f"  平均每张: {benchmark_results['avg_time_per_image']*1000:.2f} 毫秒")
